#!/bin/bash

# Usage: ./auto_extract_selfdir.sh <archive_file> [destination_folder]

ARCHIVE="$1"
DEST="$2"

if [[ -z "$ARCHIVE" ]]; then
  echo "Usage: $0 <archive_file> [destination_folder]"
  exit 1
fi

if [[ -z "$DEST" ]]; then
  BASENAME="$(basename "$ARCHIVE")"
  DEST="./${BASENAME%%.*}"
else
  DEST="$2"
fi

mkdir -p "$DEST"

case "$ARCHIVE" in
  *.tar.bz2)   tar xvjf "$ARCHIVE" -C "$DEST" ;;
  *.tar.gz)    tar xvzf "$ARCHIVE" -C "$DEST" ;;
  *.tar.xz)    tar xvJf "$ARCHIVE" -C "$DEST" ;;
  *.tar)       tar xvf  "$ARCHIVE" -C "$DEST" ;;
  *.tbz2)      tar xvjf "$ARCHIVE" -C "$DEST" ;;
  *.tgz)       tar xvzf "$ARCHIVE" -C "$DEST" ;;
  *.zip)       unzip "$ARCHIVE" -d "$DEST" ;;
  *.rar)       unrar x "$ARCHIVE" "$DEST" ;;
  *)
    echo "Unknown archive type: $ARCHIVE"
    exit 2
    ;;
esac

echo "Extracted $ARCHIVE to $DEST"
