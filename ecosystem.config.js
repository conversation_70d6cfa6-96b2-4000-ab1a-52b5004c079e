module.exports = {
  apps: [{
    name: 'upload-server',
    script: 'src/app.js',
    
    // Production settings
    instances: 'max', // Sử dụng tất cả CPU cores
    exec_mode: 'cluster', // Cluster mode cho high availability
    
    // Environment
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    
    // Logging
    log_file: 'logs/combined.log',
    out_file: 'logs/out.log',
    error_file: 'logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Auto restart settings
    watch: false, // KHÔNG watch files trong production
    ignore_watch: ['node_modules', 'uploads', 'logs'],
    
    // Memory và CPU limits
    max_memory_restart: '2G', // Restart nếu memory > 2GB
    
    // Restart settings
    restart_delay: 4000, // Delay 4s trước khi restart
    max_restarts: 10, // Max 10 restarts trong 1 phút
    min_uptime: '10s', // Minimum uptime trước khi coi là stable
    
    // Advanced settings
    kill_timeout: 5000, // Timeout để kill process
    listen_timeout: 3000, // Timeout để listen port
    
    // Health check
    health_check_grace_period: 3000,
    
    // Cron restart (optional - restart hàng ngày lúc 3AM)
    cron_restart: '0 3 * * *'
  }]
};
