const axios = require("axios");
const fs = require("fs");
const crypto = require("crypto");
const FormData = require("form-data");

const baseURL = "http://localhost:3000";

// Helper function to create a larger test file
function createLargeTestFile(sizeInMB = 25) {
  const content = "This is a test file for multi-chunk upload testing. ".repeat(1000);
  const buffer = Buffer.from(content);
  
  // Repeat to reach desired size
  const targetSize = sizeInMB * 1024 * 1024;
  const repeats = Math.ceil(targetSize / buffer.length);
  
  const buffers = [];
  for (let i = 0; i < repeats; i++) {
    buffers.push(buffer);
  }
  
  return Buffer.concat(buffers).slice(0, targetSize);
}

// Helper function to calculate combined hash (like client does)
function calculateCombinedHash(buffer, chunkSize = 64 * 1024 * 1024) {
  const finalHash = crypto.createHash('sha256');
  let offset = 0;
  
  while (offset < buffer.length) {
    const chunk = buffer.slice(offset, Math.min(offset + chunkSize, buffer.length));
    const chunkHash = crypto.createHash('sha256').update(chunk).digest('hex');
    finalHash.update(chunkHash);
    offset += chunkSize;
  }
  
  return finalHash.digest('hex');
}

// Helper function to calculate standard hash
function calculateHash(buffer) {
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

// Split buffer into chunks
function splitIntoChunks(buffer, chunkSize = 10 * 1024 * 1024) {
  const chunks = [];
  let offset = 0;
  
  while (offset < buffer.length) {
    const chunk = buffer.slice(offset, Math.min(offset + chunkSize, buffer.length));
    chunks.push(chunk);
    offset += chunkSize;
  }
  
  return chunks;
}

// Test function for multi-chunk upload
async function testMultiChunkUpload() {
  try {
    console.log("Testing multi-chunk upload service...");

    // Test 1: Check if service is running
    console.log("\n1. Testing service availability...");
    try {
      const testResponse = await axios.get(`${baseURL}/api/upload/test`);
      console.log("✓ Service is running:", testResponse.data);
    } catch (error) {
      console.log("❌ Service not running. Please start the server with: npm start");
      return;
    }

    // Test 2: Create a large test file
    console.log("\n2. Creating large test file...");
    const testBuffer = createLargeTestFile(25); // 25MB file
    const fileHash = calculateCombinedHash(testBuffer);
    const chunkSize = 10 * 1024 * 1024; // 10MB chunks
    const chunks = splitIntoChunks(testBuffer, chunkSize);
    
    console.log(`✓ Created test file: ${testBuffer.length} bytes`);
    console.log(`✓ Split into ${chunks.length} chunks`);
    console.log(`✓ File hash: ${fileHash}`);

    // Test 3: Initialize upload
    console.log("\n3. Initializing upload...");
    const initData = {
      filename: "large-test-file.txt",
      filesize: testBuffer.length,
      filehash: fileHash,
      totalChunks: chunks.length,
    };

    const initResponse = await axios.post(`${baseURL}/api/upload/init-upload`, initData);
    console.log("✓ Upload initialized:", initResponse.data);
    const uploadId = initResponse.data.uploadId;

    // Test 4: Upload all chunks
    console.log("\n4. Uploading chunks...");
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const chunkHash = calculateHash(chunk);
      
      const formData = new FormData();
      formData.append("uploadId", uploadId);
      formData.append("chunkIndex", i.toString());
      formData.append("chunkHash", chunkHash);
      formData.append("chunk", chunk, {
        filename: `chunk_${i}`,
        contentType: "application/octet-stream",
      });

      const chunkResponse = await axios.post(
        `${baseURL}/api/upload/upload-chunk`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 30000,
        }
      );
      
      console.log(`✓ Chunk ${i} uploaded:`, chunkResponse.data);
    }

    // Test 5: Check status after all chunks
    console.log("\n5. Checking status after all chunks...");
    const statusResponse = await axios.get(`${baseURL}/api/upload/upload-status`, {
      params: { uploadId },
    });
    console.log("✓ Final status:", statusResponse.data);

    // Test 6: Complete upload
    console.log("\n6. Completing upload...");
    const completeResponse = await axios.post(`${baseURL}/api/upload/complete-upload`, {
      uploadId,
    });
    console.log("✓ Upload completed:", completeResponse.data);

    console.log("\n✅ Multi-chunk upload test passed!");
  } catch (error) {
    console.error("❌ Multi-chunk test failed:", {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
  }
}

// Run tests
testMultiChunkUpload();
