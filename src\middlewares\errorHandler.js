module.exports = (err, req, res, next) => {
    console.error('Error occurred:', {
        message: err.message,
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
    });

    // Handle specific error types
    let statusCode = err.status || 500;
    let message = err.message || 'Internal Server Error';

    // Handle multer errors
    if (err.code === 'LIMIT_FILE_SIZE') {
        statusCode = 413;
        message = 'File too large';
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        statusCode = 400;
        message = 'Unexpected file field';
    } else if (err.code === 'ENOENT') {
        statusCode = 404;
        message = 'File or directory not found';
    } else if (err.code === 'EACCES') {
        statusCode = 403;
        message = 'Permission denied';
    }

    res.status(statusCode).json({ 
        error: message,
        uploadId: req.body?.uploadId || req.query?.uploadId || null,
        timestamp: new Date().toISOString()
    });
};
