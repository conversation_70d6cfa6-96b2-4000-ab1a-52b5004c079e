const axios = require("axios");
const crypto = require("crypto");
const FormData = require("form-data");

const baseURL = "http://localhost:3000";

// Create a single chunk buffer (10MB)
function createChunkBuffer(chunkIndex) {
  const baseContent = `Chunk ${chunkIndex} data: `.repeat(100);
  const filler = "A".repeat(1024);
  const content = baseContent + filler;
  const buffer = Buffer.from(content);
  
  const targetSize = 10 * 1024 * 1024; // 10MB
  const repeats = Math.ceil(targetSize / buffer.length);
  
  const buffers = [];
  for (let i = 0; i < repeats; i++) {
    buffers.push(buffer);
  }
  
  return Buffer.concat(buffers).slice(0, targetSize);
}

function calculateHash(buffer) {
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

// Upload chunk with retry
async function uploadChunkWithRetry(uploadId, chunkIndex, chunkBuffer, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const chunkHash = calculateHash(chunkBuffer);
      const formData = new FormData();
      formData.append("uploadId", uploadId);
      formData.append("chunkIndex", chunkIndex.toString());
      formData.append("chunkHash", chunkHash);
      formData.append("chunk", chunkBuffer, {
        filename: `chunk_${chunkIndex}`,
        contentType: "application/octet-stream",
      });

      const startTime = Date.now();
      const response = await axios.post(
        `${baseURL}/api/upload/upload-chunk`,
        formData,
        {
          headers: { ...formData.getHeaders() },
          timeout: 60000, // 1 minute
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        }
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      const speed = (chunkBuffer.length / 1024 / 1024) / (duration / 1000);
      
      return { success: true, duration, speed, response: response.data };
    } catch (error) {
      if (attempt === maxRetries) throw error;
      await new Promise(resolve => setTimeout(resolve, 500 * attempt));
    }
  }
}

// Test different concurrency levels
async function testConcurrencyLevels() {
  const concurrencyLevels = [1, 3, 5, 8, 10, 15];
  const testChunks = 20; // 200MB test
  
  console.log("🚀 Testing different concurrency levels...\n");
  
  for (const concurrency of concurrencyLevels) {
    console.log(`\n=== Testing Concurrency: ${concurrency} ===`);
    
    try {
      // Initialize upload
      const fileHash = "test-hash-" + Date.now();
      const initResponse = await axios.post(`${baseURL}/api/upload/init-upload`, {
        filename: `test-concurrency-${concurrency}.bin`,
        filesize: testChunks * 10 * 1024 * 1024,
        filehash: fileHash,
        totalChunks: testChunks,
      });
      
      const uploadId = initResponse.data.uploadId;
      console.log(`✓ Upload initialized: ${uploadId}`);
      
      // Upload with specific concurrency
      const startTime = Date.now();
      const results = [];
      const activeUploads = new Set();
      let completedChunks = 0;
      let totalSpeed = 0;
      
      const uploadPromises = [];
      
      for (let i = 0; i < testChunks; i++) {
        // Wait if we've reached max concurrency
        while (activeUploads.size >= concurrency) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        const chunkBuffer = createChunkBuffer(i);
        activeUploads.add(i);
        
        const uploadPromise = uploadChunkWithRetry(uploadId, i, chunkBuffer)
          .then(result => {
            completedChunks++;
            totalSpeed += result.speed;
            results[i] = result;
            activeUploads.delete(i);
            return result;
          })
          .catch(error => {
            activeUploads.delete(i);
            throw error;
          });
        
        uploadPromises.push(uploadPromise);
        
        // Small delay to prevent overwhelming
        if (concurrency > 5) {
          await new Promise(resolve => setTimeout(resolve, 20));
        }
      }
      
      // Wait for all uploads
      await Promise.all(uploadPromises);
      
      const endTime = Date.now();
      const totalDuration = endTime - startTime;
      const totalMB = testChunks * 10;
      const overallSpeed = totalMB / (totalDuration / 1000);
      const avgChunkSpeed = totalSpeed / completedChunks;
      
      console.log(`📊 Results for Concurrency ${concurrency}:`);
      console.log(`  - Total time: ${(totalDuration / 1000).toFixed(2)}s`);
      console.log(`  - Overall speed: ${overallSpeed.toFixed(2)} MB/s`);
      console.log(`  - Avg chunk speed: ${avgChunkSpeed.toFixed(2)} MB/s`);
      console.log(`  - Chunks completed: ${completedChunks}/${testChunks}`);
      
      // Clean up - don't complete upload to save time
      
    } catch (error) {
      console.error(`❌ Concurrency ${concurrency} failed:`, error.message);
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

// Run test
testConcurrencyLevels();
