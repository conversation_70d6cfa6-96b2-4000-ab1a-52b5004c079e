const extractService = require('../services/extract.service');

exports.extractAndCopy = async (req, res) => {
  try {
    const result = await extractService.extractAndCopy(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

exports.copyOnly = async (req, res) => {
  try {
    const result = await extractService.copyOnly(req.body);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};
