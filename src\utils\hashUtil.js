// utils/hashUtil.js
const crypto = require('crypto');
const fs = require('fs');

// Tính SHA‑256 của buffer (dùng cho từng chunk)
exports.hashBuffer = (buffer) => {
  return crypto.createHash('sha256').update(buffer).digest('hex');
};

// Tính SHA‑256 chuẩn của cả file (dùng khi kiểm tra chunk)
exports.hashFile = (filePath) => {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filePath);
    stream.on('data', (chunk) => hash.update(chunk));
    stream.on('end', () => resolve(hash.digest('hex')));
    stream.on('error', reject);
  });
};

// Tính hash “chunk‑hash” giống client:
// đọc file theo từng khối 64MB, băm mỗi khối bằng SHA‑256,
// rồi ghép chuỗi các hex và băm lại bằng SHA‑256.
exports.hashFileCombined = (filePath, chunkSize = 64 * 1024 * 1024) => {
  return new Promise((resolve, reject) => {
    const finalHash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filePath, { highWaterMark: chunkSize });
    stream.on('data', (chunk) => {
      const chunkHash = crypto.createHash('sha256').update(chunk).digest('hex');
      // cập nhật giá trị băm cuối cùng bằng chuỗi hex của chunk
      finalHash.update(chunkHash);
    });
    stream.on('end', () => {
      const result = finalHash.digest('hex');
      resolve(result);
    });
    stream.on('error', reject);
  });
};
