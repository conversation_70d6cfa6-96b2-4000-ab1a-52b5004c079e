const uploadService = require('../services/uploadService');

// [POST] /init-upload
exports.initUpload = async (req, res, next) => {
    try {
        // Validate request body
        const { filename, filesize, filehash, totalChunks } = req.body;
        
        if (!filename || !filesize || !totalChunks) {
            return res.status(400).json({
                error: 'Missing required fields: filename, filesize, totalChunks'
            });
        }

        if (typeof filesize !== 'number' || filesize <= 0) {
            return res.status(400).json({
                error: 'Invalid filesize'
            });
        }

        if (typeof totalChunks !== 'number' || totalChunks <= 0) {
            return res.status(400).json({
                error: 'Invalid totalChunks'
            });
        }

        const result = await uploadService.initUpload(req.body);
        res.json(result);
    } catch (err) {
        console.error('Init upload error:', err);
        next(err);
    }
};

// [POST] /upload-chunk
exports.uploadChunk = async (req, res, next) => {
    try {
        const { uploadId, chunkIndex, chunkHash } = req.body;
        
        // Validate required fields
        if (!uploadId) {
            return res.status(400).json({ error: 'Missing uploadId' });
        }

        if (chunkIndex === undefined || chunkIndex === null) {
            return res.status(400).json({ error: 'Missing chunkIndex' });
        }

        if (!chunkHash) {
            return res.status(400).json({ error: 'Missing chunkHash' });
        }

        if (!req.file) {
            return res.status(400).json({ error: 'No chunk file uploaded' });
        }

        // Validate chunk index is a valid number
        const chunkIdx = parseInt(chunkIndex);
        if (isNaN(chunkIdx) || chunkIdx < 0) {
            return res.status(400).json({ error: 'Invalid chunkIndex' });
        }

        // Process the chunk
        const ack = await uploadService.saveChunk(
            uploadId, 
            chunkIdx, 
            req.file.path, // Path to file saved by multer
            chunkHash
        );

        res.json(ack);
    } catch (err) {
        console.error('Upload chunk error:', err);
        next(err);
    }
};

// [GET] /upload-status
exports.uploadStatus = async (req, res, next) => {
    try {
        const { uploadId } = req.query;
        
        if (!uploadId) {
            return res.status(400).json({ error: 'Missing uploadId parameter' });
        }

        const status = await uploadService.getStatus(uploadId);
        res.json(status);
    } catch (err) {
        console.error('Upload status error:', err);
        next(err);
    }
};

// [POST] /complete-upload
exports.completeUpload = async (req, res, next) => {
    try {
        const { uploadId } = req.body;
        
        if (!uploadId) {
            return res.status(400).json({ error: 'Missing uploadId' });
        }

        const result = await uploadService.completeUpload(uploadId);
        res.json(result);
    } catch (err) {
        console.error('Complete upload error:', err);
        next(err);
    }
};

// [GET] /debug/uploads - Debug endpoint to list active uploads
exports.debugUploads = async (req, res, next) => {
    try {
        const uploads = await uploadService.listActiveUploads();
        res.json(uploads);
    } catch (err) {
        console.error('Debug uploads error:', err);
        next(err);
    }
};
