import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  UploadSettings,
  ChunkProgress,
  StatusMessage,
  UploadResult,
  UploadTask,
  ServerStatus,
} from '@/types/upload'
import { URL_BASE_API_UPLOAD, CHUNK_SIZE } from '@/constants/upload'
import axios from 'axios'

// --- Ultra-Fast Configuration ---
const baseURL = URL_BASE_API_UPLOAD || 'http://localhost:3000'
const OPTIMAL_CONCURRENCY = 8 // Tăng lên 8 luồng
const MAX_RETRIES = 1 // Chỉ retry 1 lần
const RETRY_DELAY = 100 // Chỉ 100ms delay

export const useUploadStore = defineStore('upload', () => {
  // --- State ---
  const uploadTasks = ref<UploadTask[]>([])
  const statusMessages = ref<StatusMessage[]>([])
  const activeTaskIndex = ref<number | null>(null)
  const serverStatus = ref<ServerStatus>({ connected: false })
  const checking = ref(false)
  const selectedFile = ref<File | null>(null)
  const isDragOver = ref(false)
  const uploading = ref(false)
  const uploadPaused = ref(false)
  const progress = ref(0)
  const currentChunk = ref(0)

  // Upload statistics
  const startTime = ref(0)
  const uploadedBytes = ref(0)
  const uploadSpeed = ref(0)
  const lastProgressUpdate = ref(0)
  const lastUploadedBytes = ref(0)

  // --- Helper functions ---
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`
  }

  function addStatusMessage(type: StatusMessage['type'], text: string) {
    statusMessages.value.unshift({
      timestamp: Date.now(),
      type,
      text,
    })
    if (statusMessages.value.length > 50) statusMessages.value = statusMessages.value.slice(0, 50)
  }

  // Hash function (SHA-256) for single buffer
  async function calcSha256(buf: ArrayBuffer): Promise<string> {
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', buf)
    return Array.prototype.map
      .call(new Uint8Array(hashBuffer), (x: number) => ('00' + x.toString(16)).slice(-2))
      .join('')
  }

  // Fast hash calculation
  async function hashFile(file: File): Promise<string> {
    const HASH_CHUNK_SIZE = 64 * 1024 * 1024 // 64MB chunks for hashing
    let offset = 0
    const chunkHashes: string[] = []

    addStatusMessage('info', `Calculating hash for ${formatFileSize(file.size)} file...`)

    while (offset < file.size) {
      const chunkEnd = Math.min(offset + HASH_CHUNK_SIZE, file.size)
      const chunk = file.slice(offset, chunkEnd)

      const arrayBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target?.result as ArrayBuffer)
        reader.onerror = reject
        reader.readAsArrayBuffer(chunk)
      })

      const chunkHash = await calcSha256(arrayBuffer)
      chunkHashes.push(chunkHash)
      offset = chunkEnd

      // Less frequent progress updates
      const progress = Math.round((offset / file.size) * 100)
      if (progress % 20 === 0) {
        addStatusMessage('info', `Hash calculation progress: ${progress}%`)
      }

      // Minimal delay
      if (chunkHashes.length % 10 === 0) {
        await new Promise((resolve) => setTimeout(resolve, 1))
      }
    }

    // Create final combined hash
    const combinedHashString = chunkHashes.join('')
    const combinedBuffer = new TextEncoder().encode(combinedHashString)
    const finalHashBuffer = await window.crypto.subtle.digest('SHA-256', combinedBuffer)

    const finalHash = Array.prototype.map
      .call(new Uint8Array(finalHashBuffer), (x: number) => ('00' + x.toString(16)).slice(-2))
      .join('')

    addStatusMessage('success', 'File hash calculation completed')
    return finalHash
  }

  // --- Upload task functions ---
  function addUploadTask(file: File, settings: UploadSettings, typeFile: string) {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE)
    
    // Ultra-fast settings
    const ultraFastSettings = {
      ...settings,
      parallelUpload: true,
      maxConcurrent: Math.min(settings.maxConcurrent || OPTIMAL_CONCURRENCY, 10), // Cap at 10
    }

    uploadTasks.value.push({
      uploadId: '',
      typeFileData: typeFile,
      file,
      filename: file.name,
      filesize: file.size,
      status: 'waiting',
      progress: 0,
      currentChunk: 0,
      totalChunks,
      chunkProgress: Array(totalChunks)
        .fill(null)
        .map(() => ({
          status: 'pending' as const,
          progress: 0,
        })),
      uploadResult: null,
      paused: false,
      settings: ultraFastSettings,
      startTime: 0,
      speed: 0,
    })

    addStatusMessage('info', `Added task: ${file.name} (${totalChunks} chunks, ${ultraFastSettings.maxConcurrent} concurrent)`)
    if (activeTaskIndex.value === null) activeTaskIndex.value = 0
  }

  // Ultra-fast chunk upload with minimal retry
  async function uploadChunk(task: UploadTask, chunkIndex: number): Promise<void> {
    const file = task.file
    const start = chunkIndex * CHUNK_SIZE
    const end = Math.min(file.size, start + CHUNK_SIZE)
    
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const blob = file.slice(start, end)
        const arrayBuffer = await blob.arrayBuffer()
        const chunkHash = await calcSha256(arrayBuffer)
        const formData = new FormData()
        
        formData.append('uploadId', task.uploadId)
        formData.append('chunkIndex', chunkIndex.toString())
        formData.append('chunkHash', chunkHash)
        formData.append('chunk', new Blob([arrayBuffer], { type: 'application/octet-stream' }))
        
        await axios.post(`${baseURL}/api/upload/upload-chunk`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: 30000, // Giảm timeout xuống 30s
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        })
        
        // Success - exit immediately
        return
        
      } catch (error: any) {
        if (attempt === MAX_RETRIES) {
          throw new Error(`Chunk ${chunkIndex} failed: ${error.message}`)
        }
        
        // Minimal retry delay
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY))
      }
    }
  }

  // --- Core upload logic ---
  async function startTask(index: number, autoExtract: boolean = false) {
    const task = uploadTasks.value[index]
    if (!task) return
    
    task.status = 'uploading'
    task.paused = false
    task.progress = 0
    task.currentChunk = 0
    task.startTime = Date.now()
    addStatusMessage('info', `Start uploading: ${task.filename}`)

    try {
      // Calculate file hash
      addStatusMessage('info', 'Calculating file hash...')
      const fileHash = await hashFile(task.file)
      
      // Initialize upload session
      addStatusMessage('info', 'Initializing upload session...')
      const res = await axios.post(`${baseURL}/api/upload/init-upload`, {
        filename: task.filename,
        filesize: task.filesize,
        filehash: fileHash,
        totalChunks: task.totalChunks,
      })
      task.uploadId = res.data.uploadId

      // Use ultra-fast parallel upload
      await uploadUltraFast(task)

      if (!task.paused) {
        // Complete upload
        addStatusMessage('info', 'Finalizing upload...')
        const result = await axios.post(`${baseURL}/api/upload/complete-upload`, {
          uploadId: task.uploadId,
        })
        
        task.uploadResult = {
          success: true,
          autoExtract: autoExtract,
          uploadId: task.uploadId,
          filename: task.filename,
          filesize: task.filesize,
          finalPath: result.data.finalPath,
          extractLog: result.data.extractLog,
        }
        task.status = 'completed'
        task.progress = 100
        addStatusMessage('success', `Upload completed: ${task.filename}`)
      }
    } catch (error: any) {
      task.status = 'error'
      task.error = error.message
      task.uploadResult = {
        success: false,
        autoExtract: false,
        uploadId: task.uploadId,
        filename: task.filename,
        error: error.message,
      }
      addStatusMessage('error', `Upload failed: ${task.filename} (${error.message})`)
    }
  }

  // Ultra-fast parallel upload - NO batching, NO delays
  async function uploadUltraFast(task: UploadTask) {
    const maxConcurrent = task.settings.maxConcurrent || OPTIMAL_CONCURRENCY
    let completedChunks = 0
    let activeUploads = 0

    addStatusMessage('info', `🚀 Ultra-fast upload: ${maxConcurrent} concurrent streams`)

    const uploadPromises: Promise<void>[] = []

    // Fire all uploads as fast as possible
    for (let i = 0; i < task.totalChunks; i++) {
      if (task.paused) {
        task.status = 'paused'
        addStatusMessage('warning', `Paused: ${task.filename}`)
        break
      }

      // Minimal concurrency control
      while (activeUploads >= maxConcurrent) {
        await new Promise(resolve => setTimeout(resolve, 1)) // 1ms wait
      }

      activeUploads++
      task.chunkProgress[i].status = 'uploading'

      const uploadPromise = uploadChunk(task, i)
        .then(() => {
          task.chunkProgress[i].status = 'completed'
          task.chunkProgress[i].progress = 100
          completedChunks++
          task.currentChunk = completedChunks
          task.progress = Math.round((completedChunks / task.totalChunks) * 100)

          // Calculate speed
          const elapsed = (Date.now() - task.startTime) / 1000
          const avgSpeed = (completedChunks * CHUNK_SIZE / 1024 / 1024) / elapsed
          task.speed = avgSpeed

          // Minimal status messages for max performance
          if (completedChunks % 10 === 0 || completedChunks === task.totalChunks) {
            addStatusMessage('info',
              `⚡ ${completedChunks}/${task.totalChunks} | ${task.progress}% | ${avgSpeed.toFixed(1)} MB/s`
            )
          }
        })
        .catch((error) => {
          task.chunkProgress[i].status = 'error'
          addStatusMessage('error', `❌ Chunk ${i + 1} failed: ${error.message}`)
          // Don't throw - continue with other chunks
        })
        .finally(() => {
          activeUploads--
        })

      uploadPromises.push(uploadPromise)
    }

    // Wait for all uploads
    await Promise.all(uploadPromises.map(p => p.catch(() => {}))) // Ignore individual failures

    const successfulChunks = task.chunkProgress.filter(c => c.status === 'completed').length
    const failedChunks = task.totalChunks - successfulChunks

    if (failedChunks > 0) {
      throw new Error(`${failedChunks} chunks failed to upload`)
    }

    addStatusMessage('success',
      `🎉 Ultra-fast upload completed: ${successfulChunks} chunks, ${task.speed.toFixed(1)} MB/s`
    )
  }

  // Legacy functions for compatibility
  async function uploadParallel(task: UploadTask) {
    return uploadUltraFast(task)
  }

  async function uploadSequential(task: UploadTask) {
    // Convert to ultra-fast for better performance
    return uploadUltraFast(task)
  }

  // --- Control functions ---
  function pauseTask(index: number) {
    const task = uploadTasks.value[index]
    if (task) {
      task.paused = true
      task.status = 'paused'
      addStatusMessage('warning', `Paused upload: ${task.filename}`)
    }
  }

  function resumeTask(index: number) {
    const task = uploadTasks.value[index]
    if (task && task.status === 'paused') {
      task.paused = false
      task.status = 'uploading'
      startTask(index)
      addStatusMessage('info', `Resumed upload: ${task.filename}`)
    }
  }

  function removeTask(index: number) {
    const task = uploadTasks.value[index]
    if (task) {
      uploadTasks.value.splice(index, 1)
      addStatusMessage('warning', `Removed upload: ${task.filename}`)
      if (uploadTasks.value.length === 0) activeTaskIndex.value = null
    }
  }

  // --- Server Status ---
  const checkServerStatus = async () => {
    checking.value = true
    try {
      const response = await axios.get(`${baseURL}/api/status`)
      serverStatus.value = response.data
      addStatusMessage('info', 'Connected to server')
      return response.data
    } catch (error: any) {
      serverStatus.value = { connected: false, message: error.message }
      addStatusMessage('error', 'Failed to connect to server')
      return null
    } finally {
      checking.value = false
    }
  }

  // --- File handling ---
  const clearFile = () => {
    selectedFile.value = null
    resetUploadState()
  }

  // --- Upload state management ---
  const resetUploadState = () => {
    uploading.value = false
    uploadPaused.value = false
    progress.value = 0
    currentChunk.value = 0
    uploadedBytes.value = 0
    uploadSpeed.value = 0
    lastProgressUpdate.value = 0
    lastUploadedBytes.value = 0
    startTime.value = 0
  }

  // Computed getters
  const getUploadSpeed = computed(() => {
    if (uploadSpeed.value === 0) return '0 B/s'
    return `${formatFileSize(uploadSpeed.value)}/s`
  })

  const getElapsedTime = computed(() => {
    return startTime.value ? Date.now() - startTime.value : 0
  })

  // Get current task performance stats
  const getCurrentTaskStats = computed(() => {
    const task = activeTaskIndex.value !== null ? uploadTasks.value[activeTaskIndex.value] : null
    if (!task) return null

    return {
      filename: task.filename,
      progress: task.progress,
      speed: task.speed,
      currentChunk: task.currentChunk,
      totalChunks: task.totalChunks,
      concurrency: task.settings.maxConcurrent,
      status: task.status
    }
  })

  // --- Expose API ---
  return {
    // State
    uploadTasks,
    activeTaskIndex,
    statusMessages,
    serverStatus,
    checking,
    selectedFile,
    isDragOver,
    uploading,
    uploadPaused,
    progress,
    currentChunk,

    // Upload stats
    uploadSpeed: getUploadSpeed,
    elapsedTime: getElapsedTime,
    currentTaskStats: getCurrentTaskStats,

    // Methods
    formatFileSize,
    addStatusMessage,
    addUploadTask,
    startTask,
    pauseTask,
    resumeTask,
    removeTask,
    checkServerStatus,
    clearFile,
    resetUploadState,
  }
})
