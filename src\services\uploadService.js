const fs = require("fs-extra");
const path = require("path");
const { v4: uuidv4 } = require("uuid");
const hashUtil = require("../utils/hashUtil");
const fileUtil = require("../utils/fileUtil");
const extractService = require("./extract.service");
const lockfile = require("proper-lockfile");

const { UPLOAD_DIR, LOCK_TIMEOUT } = require("../config/paths");

// Performance optimization: Cache for metadata to reduce file I/O
const metadataCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Helper function to get cached metadata
function getCachedMetadata(uploadId) {
  const cached = metadataCache.get(uploadId);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  return null;
}

// Helper function to set cached metadata
function setCachedMetadata(uploadId, data) {
  metadataCache.set(uploadId, {
    data: { ...data },
    timestamp: Date.now()
  });
}

// Helper function to clear cached metadata
function clearCachedMetadata(uploadId) {
  metadataCache.delete(uploadId);
}

// [POST] /init-upload
exports.initUpload = async (meta) => {
  let uploadId;
  try {
    // Validate input
    if (!meta || !meta.filename || !meta.filesize || !meta.totalChunks) {
      throw new Error(
        "Missing required metadata: filename, filesize, totalChunks"
      );
    }

    // meta: { filename, filesize, filehash, totalChunks }
    uploadId = uuidv4();
    const uploadPath = path.join(UPLOAD_DIR, uploadId);
    const metaPath = path.join(uploadPath, "meta.json");
    const tempPath = `${metaPath}.tmp`;

    // Ensure upload directory exists
    await fs.ensureDir(uploadPath);

    // Prepare metadata
    const metaData = {
      ...meta,
      uploadId,
      receivedChunks: [],
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      status: "initialized",
      errors: [],
    };

    // Write to temp file first
    await fs.writeJson(tempPath, metaData, { spaces: 2 });

    // Verify temp file
    const verification = await fs.readJson(tempPath);
    if (!verification.uploadId) {
      throw new Error("Failed to initialize upload metadata");
    }

    // Move to final location
    await fs.move(tempPath, metaPath, { overwrite: true });

    // Test lock file functionality
    try {
      const release = await lockfile.lock(metaPath, {
        retries: 0,
        realpath: false,
      });
      await release();
    } catch (lockError) {
      console.warn("Lock file test failed:", lockError.message);
    }

    return {
      uploadId,
      status: "initialized",
      message: "Upload session created successfully",
    };
  } catch (error) {
    console.error("Init upload error:", error);
    // Cleanup on error
    try {
      if (uploadId) {
        await fs.remove(path.join(UPLOAD_DIR, uploadId));
      }
    } catch (cleanupError) {
      console.error("Cleanup error:", cleanupError);
    }
    throw new Error("Failed to initialize upload: " + error.message);
  }
};

// Helper function to safely read meta.json with file locking
async function safeReadMeta(metaPath) {
  let release;
  try {
    // Acquire a read lock
    release = await lockfile.lock(metaPath, {
      retries: 5,
      retryWait: 100,
      stale: 10000, // 10 seconds stale lock protection
    });

    const meta = await fs.readJson(metaPath);
    if (!meta || !meta.uploadId) {
      throw new Error("Invalid metadata format");
    }
    return meta;
  } catch (error) {
    console.error("Error reading metadata:", error);
    throw new Error("Failed to read upload metadata: " + error.message);
  } finally {
    if (release) {
      try {
        await release();
      } catch (e) {
        console.error("Error releasing read lock:", e);
      }
    }
  }
}

// Helper function to safely write meta.json with file locking
// async function safeWriteMeta(metaPath, metaUpdate) {
//     let release;
//     try {
//         // Acquire an exclusive lock
//         release = await lockfile.lock(metaPath, {
//             retries: 5,
//             retryWait: 100,
//             stale: 10000
//         });

//         // Read current metadata
//         const currentMeta = await fs.readJson(metaPath);

//         // Merge updates with current metadata
//         const newMeta = { ...currentMeta, ...metaUpdate };

//         // Write to temporary file
//         const tempPath = `${metaPath}.tmp`;
//         await fs.writeJson(tempPath, newMeta, { spaces: 2 });

//         // Verify the temporary file
//         const verification = await fs.readJson(tempPath);
//         if (!verification.uploadId) {
//             throw new Error('Metadata verification failed');
//         }

//         // Atomic replace
//         await fs.rename(tempPath, metaPath);

//         return newMeta;
//     } catch (error) {
//         console.error('Error writing metadata:', error);
//         throw new Error('Failed to update upload metadata: ' + error.message);
//     } finally {
//         if (release) {
//             try {
//                 await release();
//             } catch (e) {
//                 console.error('Error releasing write lock:', e);
//             }
//         }
//     }
// }

async function safeWriteMeta(metaPath, metaUpdate) {
  let release;
  try {
    release = await lockfile.lock(metaPath, {
      retries: 5,
      retryWait: 100,
      stale: 10000,
    });
    const currentMeta = await fs.readJson(metaPath);

    let received = currentMeta.receivedChunks || [];
    if (Array.isArray(metaUpdate.receivedChunks)) {
      received = Array.from(
        new Set([...received, ...metaUpdate.receivedChunks.map(Number)])
      ).sort((a, b) => a - b);
    }

    const newMeta = { ...currentMeta, ...metaUpdate, receivedChunks: received };
    const tmp = `${metaPath}.tmp`;
    await fs.writeJson(tmp, newMeta, { spaces: 2 });
    await fs.rename(tmp, metaPath);
    return newMeta;
  } finally {
    if (release) await release();
  }
}

// [POST] /upload-chunk
// exports.saveChunk = async (uploadId, chunkIndex, chunkPath, chunkHash) => {
//   const uploadPath = path.join(UPLOAD_DIR, uploadId);
//   const metaPath = path.join(uploadPath, "meta.json");
//   const finalChunkPath = path.join(uploadPath, `chunk_${chunkIndex}`);

//   if (!(await fs.pathExists(metaPath))) {
//     throw new Error("UploadId not found");
//   }

//   let meta;
//   try {
//     // Verify hash if provided
//     if (chunkHash) {
//       const realHash = await hashUtil.hashFile(chunkPath);
//       if (realHash !== chunkHash) {
//         await fs.remove(chunkPath);
//         throw new Error("Chunk hash mismatch");
//       }
//     }

//     // Move chunk to final location if it's not already there
//     if (chunkPath !== finalChunkPath) {
//       await fs.move(chunkPath, finalChunkPath, { overwrite: true });
//     }

//     // Update metadata with file locking
//     meta = await safeReadMeta(metaPath);

//     // if (!meta.receivedChunks.includes(Number(chunkIndex))) {
//     //     const updates = {
//     //         receivedChunks: [...meta.receivedChunks, Number(chunkIndex)],
//     //         lastUpdated: new Date().toISOString()
//     //     };

//     //     meta = await safeWriteMeta(metaPath, updates);
//     // }

//     if (!meta.receivedChunks.includes(chunkIdx)) {
//       await safeWriteMeta(metaPath, {
//         receivedChunks: [chunkIdx],
//         lastUpdated: new Date().toISOString(),
//       });
//     }

//     return {
//       status: "ok",
//       chunkIndex,
//       received: meta.receivedChunks.length,
//       total: meta.totalChunks,
//     };
//   } catch (error) {
//     // Cleanup on error
//     try {
//       await fs.remove(chunkPath);
//       await fs.remove(finalChunkPath);
//     } catch (cleanupError) {
//       console.error("Cleanup error:", cleanupError);
//     }

//     throw new Error(`Chunk upload failed: ${error.message}`);
//   }
// };

// [POST] /upload-chunk
exports.saveChunk = async (uploadId, chunkIndex, chunkPath, chunkHash) => {
  const uploadPath = path.join(UPLOAD_DIR, uploadId);
  const metaPath = path.join(uploadPath, "meta.json");
  const finalChunkPath = path.join(uploadPath, `chunk_${chunkIndex}`);

  if (!(await fs.pathExists(metaPath))) {
    throw new Error("UploadId not found");
  }

  try {
    // Xác thực hash của chunk
    if (chunkHash) {
      const realHash = await hashUtil.hashFile(chunkPath);
      if (realHash !== chunkHash) {
        await fs.remove(chunkPath);
        throw new Error("Chunk hash mismatch");
      }
    }

    // Di chuyển file đến thư mục đích
    if (chunkPath !== finalChunkPath) {
      await fs.move(chunkPath, finalChunkPath, { overwrite: true });
    }

    // Đọc metadata hiện tại
    let meta = await safeReadMeta(metaPath);

    // Nếu chưa có chỉ số chunk này thì thêm vào metadata
    const idx = Number(chunkIndex);
    if (!meta.receivedChunks.includes(idx)) {
      await safeWriteMeta(metaPath, {
        receivedChunks: [idx],
        lastUpdated: new Date().toISOString(),
      });
      // đọc lại meta sau khi ghi
      meta = await safeReadMeta(metaPath);
    }

    return {
      status: "ok",
      chunkIndex: idx,
      received: meta.receivedChunks.length,
      total: meta.totalChunks,
    };
  } catch (error) {
    // Xử lý lỗi: xóa file tạm
    try {
      await fs.remove(chunkPath);
      await fs.remove(finalChunkPath);
    } catch (cleanupError) {
      console.error("Cleanup error:", cleanupError);
    }
    throw new Error(`Chunk upload failed: ${error.message}`);
  }
};

// [GET] /upload-status
exports.getStatus = async (uploadId) => {
  const uploadPath = path.join(UPLOAD_DIR, uploadId);
  const metaPath = path.join(uploadPath, "meta.json");

  try {
    if (!(await fs.pathExists(metaPath))) {
      throw new Error("UploadId not found");
    }

    // Use safe read helper
    const meta = await safeReadMeta(metaPath);

    // Calculate progress
    const progress = (meta.receivedChunks.length / meta.totalChunks) * 100;

    // Determine status
    let status = "uploading";
    if (meta.receivedChunks.length === meta.totalChunks) {
      status = "ready-to-merge";
    } else if (meta.receivedChunks.length === 0) {
      status = "initialized";
    }

    return {
      uploadId: meta.uploadId,
      receivedChunks: meta.receivedChunks,
      totalChunks: meta.totalChunks,
      filename: meta.filename,
      filesize: meta.filesize,
      progress: Math.round(progress),
      status: status,
      createdAt: meta.createdAt,
      lastUpdated: meta.lastUpdated || meta.createdAt,
    };
  } catch (error) {
    console.error("Error getting upload status:", error);
    throw new Error(`Failed to get upload status: ${error.message}`);
  }
};

// [POST] /complete-upload
exports.completeUpload = async (uploadId) => {
  const uploadPath = path.join(UPLOAD_DIR, uploadId);
  const metaPath = path.join(uploadPath, "meta.json");

  try {
    if (!(await fs.pathExists(metaPath))) {
      throw new Error("UploadId not found");
    }

    // Use safe read to get metadata
    const meta = await safeReadMeta(metaPath);

    // Verify all chunks are received
    if (meta.receivedChunks.length !== meta.totalChunks) {
      const missingChunks = [];
      for (let i = 0; i < meta.totalChunks; i++) {
        if (!meta.receivedChunks.includes(i)) {
          missingChunks.push(i);
        }
      }
      throw new Error(`Missing chunks: ${missingChunks.join(", ")}`);
    }

    // Sort chunks to ensure correct order
    const sortedChunks = [...meta.receivedChunks].sort((a, b) => a - b);

    // Merge file using streams for better memory efficiency
    const finalFilePath = path.join(uploadPath, meta.filename);
    const writeStream = fs.createWriteStream(finalFilePath);

    try {
      for (let i = 0; i < meta.totalChunks; i++) {
        const chunkPath = path.join(uploadPath, `chunk_${i}`);

        if (!(await fs.pathExists(chunkPath))) {
          throw new Error(`Missing chunk file: chunk_${i}`);
        }

        // Read and write chunk data
        const chunkData = await fs.readFile(chunkPath);

        // Write chunk to final file
        await new Promise((resolve, reject) => {
          writeStream.write(chunkData, (error) => {
            if (error) reject(error);
            else resolve();
          });
        });
      }

      // Close the write stream
      writeStream.end();

      // Wait for write stream to finish
      await new Promise((resolve, reject) => {
        writeStream.on("finish", resolve);
        writeStream.on("error", reject);
      });
    } catch (error) {
      writeStream.destroy();
      await fileUtil.safeRemove(finalFilePath);
      throw error;
    }

    // Verify final file hash if provided
    // if (meta.filehash) {
    //     const fileHash = await hashUtil.hashFile(finalFilePath);
    //     if (fileHash !== meta.filehash) {
    //         await fileUtil.safeRemove(finalFilePath);
    //         throw new Error('Final file hash mismatch');
    //     }
    // }

    if (meta.filehash) {
      const fileHash = await hashUtil.hashFileCombined(finalFilePath);
      if (fileHash !== meta.filehash) {
        await fileUtil.safeRemove(finalFilePath);
        throw new Error("Final file hash mismatch");
      }
    }

    // Update metadata to mark as completed
    await safeWriteMeta(metaPath, {
      status: "completed",
      completedAt: new Date().toISOString(),
      finalFilePath: finalFilePath,
    });

    // Extract file if it's an archive
    let extractLog = null;
    try {
      extractLog = await extractService.extract(finalFilePath, uploadPath);
    } catch (extractError) {
      console.warn("Extraction failed:", extractError.message);
      extractLog = `Extraction failed: ${extractError.message}`;
    }

    // Clean up chunk files after successful merge
    try {
      for (let i = 0; i < meta.totalChunks; i++) {
        const chunkPath = path.join(uploadPath, `chunk_${i}`);
        await fileUtil.safeRemove(chunkPath);
      }
    } catch (cleanupError) {
      console.warn("Chunk cleanup failed:", cleanupError);
    }

    return {
      status: "completed",
      uploadId,
      filename: meta.filename,
      filesize: meta.filesize,
      finalPath: finalFilePath,
      extractLog,
    };
  } catch (error) {
    console.error("Complete upload error:", error);

    // Update metadata with error status
    try {
      await safeWriteMeta(metaPath, {
        status: "failed",
        error: error.message,
        failedAt: new Date().toISOString(),
      });
    } catch (metaError) {
      console.error("Failed to update error status:", metaError);
    }

    throw new Error(`Upload completion failed: ${error.message}`);
  }
};

// Debug function to list active uploads
exports.listActiveUploads = async () => {
  try {
    const uploads = [];
    const uploadDirs = await fs.readdir(UPLOAD_DIR);

    for (const dir of uploadDirs) {
      const uploadPath = path.join(UPLOAD_DIR, dir);
      const metaPath = path.join(uploadPath, "meta.json");

      if (await fs.pathExists(metaPath)) {
        try {
          const meta = await fs.readJson(metaPath);
          uploads.push({
            uploadId: meta.uploadId,
            filename: meta.filename,
            status: meta.status,
            progress: Math.round(
              (meta.receivedChunks.length / meta.totalChunks) * 100
            ),
            receivedChunks: meta.receivedChunks.length,
            totalChunks: meta.totalChunks,
            createdAt: meta.createdAt,
            lastUpdated: meta.lastUpdated,
          });
        } catch (error) {
          console.error(`Error reading metadata for ${dir}:`, error);
        }
      }
    }

    return uploads;
  } catch (error) {
    console.error("Error listing uploads:", error);
    throw new Error("Failed to list uploads");
  }
};
