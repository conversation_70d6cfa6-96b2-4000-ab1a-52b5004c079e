const axios = require("axios");
const crypto = require("crypto");
const FormData = require("form-data");

const baseURL = "http://localhost:3000";

// Helper function to create test buffer
function createTestBuffer(sizeInMB) {
  const content = "A".repeat(1024); // 1KB of 'A'
  const buffer = Buffer.from(content);
  
  const targetSize = sizeInMB * 1024 * 1024;
  const repeats = Math.ceil(targetSize / buffer.length);
  
  const buffers = [];
  for (let i = 0; i < repeats; i++) {
    buffers.push(buffer);
  }
  
  return Buffer.concat(buffers).slice(0, targetSize);
}

// Helper function to calculate combined hash
function calculateCombinedHash(buffer, chunkSize = 64 * 1024 * 1024) {
  const finalHash = crypto.createHash('sha256');
  let offset = 0;
  
  while (offset < buffer.length) {
    const chunk = buffer.slice(offset, Math.min(offset + chunkSize, buffer.length));
    const chunkHash = crypto.createHash('sha256').update(chunk).digest('hex');
    finalHash.update(chunkHash);
    offset += chunkSize;
  }
  
  return finalHash.digest('hex');
}

// Helper function to calculate standard hash
function calculateHash(buffer) {
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

// Test different chunk sizes
async function testDifferentSizes() {
  const sizes = [1, 5, 10, 15, 20]; // MB
  
  for (const size of sizes) {
    console.log(`\n=== Testing ${size}MB chunk ===`);
    
    try {
      // Create test buffer
      const testBuffer = createTestBuffer(size);
      const fileHash = calculateCombinedHash(testBuffer);
      
      console.log(`Created ${size}MB buffer: ${testBuffer.length} bytes`);
      
      // Initialize upload
      const initResponse = await axios.post(`${baseURL}/api/upload/init-upload`, {
        filename: `test-${size}mb.txt`,
        filesize: testBuffer.length,
        filehash: fileHash,
        totalChunks: 1,
      });
      
      const uploadId = initResponse.data.uploadId;
      console.log(`✓ Upload initialized: ${uploadId}`);
      
      // Upload single chunk
      const chunkHash = calculateHash(testBuffer);
      const formData = new FormData();
      formData.append("uploadId", uploadId);
      formData.append("chunkIndex", "0");
      formData.append("chunkHash", chunkHash);
      formData.append("chunk", testBuffer, {
        filename: "chunk_0",
        contentType: "application/octet-stream",
      });
      
      console.log(`Uploading ${size}MB chunk...`);
      const startTime = Date.now();
      
      const chunkResponse = await axios.post(
        `${baseURL}/api/upload/upload-chunk`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
          timeout: 120000, // 2 minutes
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        }
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`✓ ${size}MB chunk uploaded in ${duration}ms:`, chunkResponse.data);
      
      // Complete upload
      const completeResponse = await axios.post(`${baseURL}/api/upload/complete-upload`, {
        uploadId,
      });
      
      console.log(`✓ ${size}MB upload completed:`, completeResponse.data.status);
      
    } catch (error) {
      console.error(`❌ ${size}MB test failed:`, {
        message: error.message,
        code: error.code,
        response: error.response?.data,
        status: error.response?.status,
      });
      
      // If this size fails, don't test larger sizes
      if (error.code === 'ECONNRESET' || error.code === 'ECONNABORTED') {
        console.log(`Connection issues at ${size}MB - stopping tests`);
        break;
      }
    }
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Run tests
testDifferentSizes();
