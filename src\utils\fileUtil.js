const fs = require('fs-extra');
const path = require('path');

// Ensure directory exists
exports.ensureDir = async (dirPath) => {
    try {
        await fs.ensureDir(dirPath);
        return true;
    } catch (error) {
        console.error('Error creating directory:', error);
        throw new Error(`Failed to create directory: ${dirPath}`);
    }
};

// Check if file exists
exports.fileExists = async (filePath) => {
    try {
        return await fs.pathExists(filePath);
    } catch (error) {
        return false;
    }
};

// Safe file removal
exports.safeRemove = async (filePath) => {
    try {
        if (await fs.pathExists(filePath)) {
            await fs.remove(filePath);
        }
    } catch (error) {
        console.error('Error removing file:', error);
    }
};

// Get file size
exports.getFileSize = async (filePath) => {
    try {
        const stats = await fs.stat(filePath);
        return stats.size;
    } catch (error) {
        throw new Error(`Failed to get file size: ${error.message}`);
    }
};