const { spawn } = require('child_process');
const axios = require('axios');

async function waitForServer(url, timeout = 10000) {
    const start = Date.now();
    while (Date.now() - start < timeout) {
        try {
            await axios.get(url);
            return true;
        } catch (error) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    return false;
}

async function runTests() {
    console.log('Starting server...');
    
    // Start the server
    const server = spawn('node', ['src/app.js'], {
        stdio: 'pipe',
        detached: false
    });
    
    server.stdout.on('data', (data) => {
        console.log(`Server: ${data}`);
    });
    
    server.stderr.on('data', (data) => {
        console.error(`Server Error: ${data}`);
    });
    
    try {
        // Wait for server to start
        console.log('Waiting for server to start...');
        const serverReady = await waitForServer('http://localhost:3000/api/upload/test');
        
        if (!serverReady) {
            console.error('Server failed to start within timeout');
            server.kill();
            return;
        }
        
        console.log('Server is ready, running tests...');
        
        // Run the test
        const testProcess = spawn('node', ['test-upload.js'], {
            stdio: 'inherit'
        });
        
        testProcess.on('close', (code) => {
            console.log(`Tests completed with code: ${code}`);
            server.kill();
        });
        
    } catch (error) {
        console.error('Error running tests:', error);
        server.kill();
    }
}

runTests();