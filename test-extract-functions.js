const axios = require("axios");
const fs = require("fs-extra");
const path = require("path");

const baseURL = "http://localhost:3000";

// Test extract functions
async function testExtractFunctions() {
  try {
    console.log("🧪 Testing Extract Functions...");

    // Test 1: Check if service is running
    console.log("\n1. Testing service availability...");
    try {
      const testResponse = await axios.get(`${baseURL}/api/upload/test`);
      console.log("✓ Service is running:", testResponse.data);
    } catch (error) {
      console.log("❌ Service not running. Please start the server with: npm start");
      return;
    }

    // Create test directories and files
    const testDir = path.join(__dirname, "test-extract");
    const srcDir = path.join(testDir, "source");
    const destDir = path.join(testDir, "destination");
    const tempExtractDir = path.join(testDir, "temp-extract");
    
    // Clean up and create test directories
    await fs.remove(testDir);
    await fs.ensureDir(srcDir);
    await fs.ensureDir(destDir);
    await fs.ensureDir(tempExtractDir);

    // Create test files
    const testFile1 = path.join(srcDir, "test1.txt");
    const testFile2 = path.join(srcDir, "test2.txt");
    const testSubDir = path.join(srcDir, "subdir");
    const testFile3 = path.join(testSubDir, "test3.txt");

    await fs.writeFile(testFile1, "This is test file 1");
    await fs.writeFile(testFile2, "This is test file 2");
    await fs.ensureDir(testSubDir);
    await fs.writeFile(testFile3, "This is test file 3 in subdirectory");

    console.log("✓ Test files created");

    // Test 2: Test copyOnly function
    console.log("\n2. Testing copyOnly function...");
    
    try {
      const copyOnlyResponse = await axios.post(`${baseURL}/api/extract/copy-only`, {
        srcDir: srcDir,
        destDir: destDir
      });
      
      console.log("✓ copyOnly response:", {
        fileCount: copyOnlyResponse.data.fileCount,
        message: copyOnlyResponse.data.message,
        copiedObjects: copyOnlyResponse.data.copiedObjects?.length || 0
      });
      
      // Verify files were copied
      const copiedFiles = await fs.readdir(destDir);
      console.log("✓ Files copied:", copiedFiles);
      
    } catch (error) {
      console.error("❌ copyOnly failed:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    }

    // Test 3: Test copyOnly with single file
    console.log("\n3. Testing copyOnly with single file...");
    
    const singleFileDestDir = path.join(testDir, "single-file-dest");
    await fs.ensureDir(singleFileDestDir);
    
    try {
      const singleFileResponse = await axios.post(`${baseURL}/api/extract/copy-only`, {
        srcDir: testFile1, // Single file instead of directory
        destDir: singleFileDestDir
      });
      
      console.log("✓ Single file copy response:", {
        fileCount: singleFileResponse.data.fileCount,
        message: singleFileResponse.data.message,
        copiedObjects: singleFileResponse.data.copiedObjects?.length || 0
      });
      
    } catch (error) {
      console.error("❌ Single file copy failed:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    }

    // Test 4: Test extractAndCopy function (simulate)
    console.log("\n4. Testing extractAndCopy function...");
    
    // Create a fake archive file for testing
    const fakeArchive = path.join(testDir, "fake-archive.zip");
    await fs.writeFile(fakeArchive, "This is a fake archive file");
    
    // Create a fake extraction script that just copies files
    const fakeScript = path.join(testDir, "fake-extract.sh");
    const scriptContent = `#!/bin/bash
# Fake extraction script for testing
echo "Fake extraction from $1 to $2"
# Copy some test files to simulate extraction
mkdir -p "$2"
echo "Extracted file 1" > "$2/extracted1.txt"
echo "Extracted file 2" > "$2/extracted2.txt"
mkdir -p "$2/extracted-dir"
echo "Extracted file in dir" > "$2/extracted-dir/file.txt"
`;
    
    await fs.writeFile(fakeScript, scriptContent);
    
    // Make script executable (on Unix systems)
    try {
      await fs.chmod(fakeScript, '755');
    } catch (error) {
      console.log("Note: Could not make script executable (Windows?)");
    }
    
    const extractDestDir = path.join(testDir, "extract-destination");
    await fs.ensureDir(extractDestDir);
    
    try {
      const extractResponse = await axios.post(`${baseURL}/api/extract/extract-and-copy`, {
        archiveFile: fakeArchive,
        tempExtractDir: tempExtractDir,
        finalDestDir: extractDestDir,
        scriptPath: fakeScript
      });
      
      console.log("✓ extractAndCopy response:", {
        fileCount: extractResponse.data.fileCount,
        message: extractResponse.data.message,
        durationSeconds: extractResponse.data.durationSeconds,
        copiedObjects: extractResponse.data.copiedObjects?.length || 0
      });
      
    } catch (error) {
      console.error("❌ extractAndCopy failed:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    }

    // Test 5: Test error cases
    console.log("\n5. Testing error cases...");
    
    // Test copyOnly with missing parameters
    try {
      await axios.post(`${baseURL}/api/extract/copy-only`, {
        // Missing srcDir and destDir
      });
      console.log("❌ Should have failed with missing parameters");
    } catch (error) {
      if (error.response?.status === 500) {
        console.log("✓ Correctly rejected missing parameters:", error.response.data.error);
      }
    }
    
    // Test copyOnly with non-existent source
    try {
      await axios.post(`${baseURL}/api/extract/copy-only`, {
        srcDir: "/non/existent/path",
        destDir: destDir
      });
      console.log("❌ Should have failed with non-existent source");
    } catch (error) {
      if (error.response?.status === 500) {
        console.log("✓ Correctly rejected non-existent source:", error.response.data.error);
      }
    }

    // Test extractAndCopy with missing parameters
    try {
      await axios.post(`${baseURL}/api/extract/extract-and-copy`, {
        // Missing required parameters
        archiveFile: fakeArchive
      });
      console.log("❌ Should have failed with missing parameters");
    } catch (error) {
      if (error.response?.status === 500) {
        console.log("✓ Correctly rejected missing parameters:", error.response.data.error);
      }
    }

    // Clean up test files
    console.log("\n6. Cleaning up test files...");
    await fs.remove(testDir);
    console.log("✓ Test files cleaned up");

    console.log("\n✅ Extract functions test completed!");

  } catch (error) {
    console.error("❌ Extract functions test failed:", {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
  }
}

// Run tests
testExtractFunctions();
