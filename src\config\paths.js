const path = require("path");

// C<PERSON>u hình đường dẫn tập trung
const config = {
  // Th<PERSON> mục uploads ở root của project (ngoài src)
  UPLOAD_DIR: path.join(process.cwd(), "uploads"),

  // <PERSON><PERSON><PERSON> mục scripts
  SCRIPTS_DIR: path.join(process.cwd(), "scripts"),

  // Kích thước chunk mặc định
  CHUNK_SIZE: 10 * 1024 * 1024, // 10MB

  // Cấu hình khác - Sử dụng số thông thường cho compatibility
  MAX_FILE_SIZE: 50 * 1024 * 1024 * 1024 * 1024, // 50TB (sử dụng số lớn)
  MAX_CHUNKS: 5000, // Tăng số chunk tối đa

  // Timeout cho các thao tác
  UPLOAD_TIMEOUT: 30000, // 30 seconds
  LOCK_TIMEOUT: 10000, // 10 seconds

  // Cleanup settings
  CLEANUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  MAX_UPLOAD_AGE: 7 * 24 * 60 * 60 * 1000, // 7 days
};

module.exports = config;
