const axios = require("axios");
const fs = require("fs");
const crypto = require("crypto");
const FormData = require("form-data");

const baseURL = "http://localhost:3000";

// Helper function to create a 50MB test file
function create50MBTestFile() {
  const content = "This is a test file for 50MB upload testing. ".repeat(1000);
  const buffer = Buffer.from(content);
  
  // Create exactly 50MB
  const targetSize = 50 * 1024 * 1024; // 50MB
  const repeats = Math.ceil(targetSize / buffer.length);
  
  const buffers = [];
  for (let i = 0; i < repeats; i++) {
    buffers.push(buffer);
  }
  
  return Buffer.concat(buffers).slice(0, targetSize);
}

// Helper function to calculate combined hash (like client does)
function calculateCombinedHash(buffer, chunkSize = 64 * 1024 * 1024) {
  const finalHash = crypto.createHash('sha256');
  let offset = 0;
  
  while (offset < buffer.length) {
    const chunk = buffer.slice(offset, Math.min(offset + chunkSize, buffer.length));
    const chunkHash = crypto.createHash('sha256').update(chunk).digest('hex');
    finalHash.update(chunkHash);
    offset += chunkSize;
  }
  
  return finalHash.digest('hex');
}

// Helper function to calculate standard hash
function calculateHash(buffer) {
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

// Split buffer into chunks
function splitIntoChunks(buffer, chunkSize = 10 * 1024 * 1024) {
  const chunks = [];
  let offset = 0;
  
  while (offset < buffer.length) {
    const chunk = buffer.slice(offset, Math.min(offset + chunkSize, buffer.length));
    chunks.push(chunk);
    offset += chunkSize;
  }
  
  return chunks;
}

// Test function for 50MB upload
async function test50MBUpload() {
  try {
    console.log("Testing 50MB upload...");

    // Test 1: Check if service is running
    console.log("\n1. Testing service availability...");
    try {
      const testResponse = await axios.get(`${baseURL}/api/upload/test`);
      console.log("✓ Service is running:", testResponse.data);
    } catch (error) {
      console.log("❌ Service not running. Please start the server with: npm start");
      return;
    }

    // Test 2: Create a 50MB test file
    console.log("\n2. Creating 50MB test file...");
    const testBuffer = create50MBTestFile();
    const fileHash = calculateCombinedHash(testBuffer);
    const chunkSize = 10 * 1024 * 1024; // 10MB chunks
    const chunks = splitIntoChunks(testBuffer, chunkSize);
    
    console.log(`✓ Created test file: ${testBuffer.length} bytes (${(testBuffer.length / 1024 / 1024).toFixed(2)} MB)`);
    console.log(`✓ Split into ${chunks.length} chunks`);
    console.log(`✓ File hash: ${fileHash}`);
    
    // Log chunk sizes
    chunks.forEach((chunk, i) => {
      console.log(`  Chunk ${i}: ${chunk.length} bytes (${(chunk.length / 1024 / 1024).toFixed(2)} MB)`);
    });

    // Test 3: Initialize upload
    console.log("\n3. Initializing upload...");
    const initData = {
      filename: "test-50mb-file.txt",
      filesize: testBuffer.length,
      filehash: fileHash,
      totalChunks: chunks.length,
    };

    const initResponse = await axios.post(`${baseURL}/api/upload/init-upload`, initData);
    console.log("✓ Upload initialized:", initResponse.data);
    const uploadId = initResponse.data.uploadId;

    // Test 4: Upload all chunks with detailed logging
    console.log("\n4. Uploading chunks...");
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const chunkHash = calculateHash(chunk);
      
      console.log(`\nUploading chunk ${i}:`);
      console.log(`  Size: ${chunk.length} bytes (${(chunk.length / 1024 / 1024).toFixed(2)} MB)`);
      console.log(`  Hash: ${chunkHash}`);
      
      const formData = new FormData();
      formData.append("uploadId", uploadId);
      formData.append("chunkIndex", i.toString());
      formData.append("chunkHash", chunkHash);
      formData.append("chunk", chunk, {
        filename: `chunk_${i}`,
        contentType: "application/octet-stream",
      });

      try {
        const startTime = Date.now();
        const chunkResponse = await axios.post(
          `${baseURL}/api/upload/upload-chunk`,
          formData,
          {
            headers: {
              ...formData.getHeaders(),
            },
            timeout: 120000, // 2 minutes timeout
            maxContentLength: Infinity,
            maxBodyLength: Infinity,
          }
        );
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`✓ Chunk ${i} uploaded successfully in ${duration}ms:`, chunkResponse.data);
      } catch (error) {
        console.error(`❌ Chunk ${i} upload failed:`, {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          code: error.code,
        });
        
        // Check server logs for more details
        console.log("\nChecking upload status for debugging...");
        try {
          const statusResponse = await axios.get(`${baseURL}/api/upload/upload-status`, {
            params: { uploadId },
          });
          console.log("Current status:", statusResponse.data);
        } catch (statusError) {
          console.error("Failed to get status:", statusError.message);
        }
        
        throw error;
      }
    }

    // Test 5: Check status after all chunks
    console.log("\n5. Checking status after all chunks...");
    const statusResponse = await axios.get(`${baseURL}/api/upload/upload-status`, {
      params: { uploadId },
    });
    console.log("✓ Final status:", statusResponse.data);

    // Test 6: Complete upload
    console.log("\n6. Completing upload...");
    const completeResponse = await axios.post(`${baseURL}/api/upload/complete-upload`, {
      uploadId,
    });
    console.log("✓ Upload completed:", completeResponse.data);

    console.log("\n✅ 50MB upload test passed!");
  } catch (error) {
    console.error("❌ 50MB test failed:", {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      code: error.code,
    });
  }
}

// Run tests
test50MBUpload();
