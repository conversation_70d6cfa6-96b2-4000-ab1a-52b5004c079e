const axios = require("axios");
const crypto = require("crypto");
const FormData = require("form-data");

const baseURL = "http://localhost:3000";

// Create a single chunk buffer (10MB)
function createChunkBuffer(chunkIndex) {
  const baseContent = `Chunk ${chunkIndex} data: `.repeat(100);
  const filler = "A".repeat(1024);
  const content = baseContent + filler;
  const buffer = Buffer.from(content);
  
  const targetSize = 10 * 1024 * 1024; // 10MB
  const repeats = Math.ceil(targetSize / buffer.length);
  
  const buffers = [];
  for (let i = 0; i < repeats; i++) {
    buffers.push(buffer);
  }
  
  return Buffer.concat(buffers).slice(0, targetSize);
}

function calculateHash(buffer) {
  return crypto.createHash("sha256").update(buffer).digest("hex");
}

function calculateCombinedHash(totalChunks) {
  const finalHash = crypto.createHash('sha256');
  
  for (let i = 0; i < totalChunks; i++) {
    const chunkBuffer = createChunkBuffer(i);
    const chunkHash = crypto.createHash('sha256').update(chunkBuffer).digest('hex');
    finalHash.update(chunkHash);
  }
  
  return finalHash.digest('hex');
}

// Upload chunk with better retry mechanism
async function uploadChunkWithRetry(uploadId, chunkIndex, chunkBuffer, maxRetries = 5) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const chunkHash = calculateHash(chunkBuffer);
      const formData = new FormData();
      formData.append("uploadId", uploadId);
      formData.append("chunkIndex", chunkIndex.toString());
      formData.append("chunkHash", chunkHash);
      formData.append("chunk", chunkBuffer, {
        filename: `chunk_${chunkIndex}`,
        contentType: "application/octet-stream",
      });

      const startTime = Date.now();
      const response = await axios.post(
        `${baseURL}/api/upload/upload-chunk`,
        formData,
        {
          headers: { ...formData.getHeaders() },
          timeout: 60000, // 1 minute
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        }
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      const speed = (chunkBuffer.length / 1024 / 1024) / (duration / 1000);
      
      return { success: true, duration, speed, response: response.data };
    } catch (error) {
      console.log(`⚠️  Chunk ${chunkIndex} attempt ${attempt}/${maxRetries} failed: ${error.message}`);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Progressive backoff: wait longer between retries
      const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10s
      console.log(`   Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
}

// Conservative parallel upload with lower concurrency
async function uploadChunksConservative(uploadId, totalChunks, maxConcurrency = 3) {
  const results = [];
  let completedChunks = 0;
  let totalSpeed = 0;
  let failedChunks = 0;
  
  console.log(`Starting conservative upload with max concurrency: ${maxConcurrency}`);
  
  // Process chunks in batches to avoid overwhelming server
  const batchSize = maxConcurrency;
  
  for (let batchStart = 0; batchStart < totalChunks; batchStart += batchSize) {
    const batchEnd = Math.min(batchStart + batchSize, totalChunks);
    const batchPromises = [];
    
    console.log(`\nProcessing batch ${Math.floor(batchStart/batchSize) + 1}: chunks ${batchStart}-${batchEnd-1}`);
    
    for (let i = batchStart; i < batchEnd; i++) {
      const chunkBuffer = createChunkBuffer(i);
      
      const uploadPromise = uploadChunkWithRetry(uploadId, i, chunkBuffer)
        .then(result => {
          completedChunks++;
          totalSpeed += result.speed;
          const avgSpeed = totalSpeed / completedChunks;
          const progress = (completedChunks / totalChunks * 100).toFixed(1);
          
          console.log(`✓ Chunk ${i}: ${result.speed.toFixed(2)} MB/s | Progress: ${progress}% | Avg: ${avgSpeed.toFixed(2)} MB/s`);
          results[i] = result;
          return result;
        })
        .catch(error => {
          failedChunks++;
          console.error(`❌ Chunk ${i} failed permanently: ${error.message}`);
          throw error;
        });
      
      batchPromises.push(uploadPromise);
    }
    
    // Wait for current batch to complete before starting next batch
    try {
      await Promise.all(batchPromises);
      console.log(`✓ Batch completed successfully`);
      
      // Small delay between batches to let server recover
      if (batchEnd < totalChunks) {
        console.log(`   Waiting 2s before next batch...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error(`❌ Batch failed, stopping upload`);
      throw error;
    }
  }
  
  console.log(`\n📊 Upload Summary: ${completedChunks} successful, ${failedChunks} failed`);
  return results;
}

// Test stable upload
async function testStableUpload() {
  try {
    console.log("🛡️  Testing stable upload with conservative settings...");

    // Test 1: Check service
    console.log("\n1. Testing service availability...");
    const testResponse = await axios.get(`${baseURL}/api/upload/test`);
    console.log("✓ Service is running:", testResponse.data);

    // Test 2: Test with smaller file first (200MB = 20 chunks)
    const testChunks = 20;
    const totalSize = testChunks * 10 * 1024 * 1024;
    const fileHash = calculateCombinedHash(testChunks);
    
    console.log(`\n2. Testing ${testChunks} chunks (${(totalSize/1024/1024).toFixed(0)} MB)...`);
    console.log(`   File hash: ${fileHash}`);

    // Test 3: Initialize upload
    console.log("\n3. Initializing upload...");
    const initResponse = await axios.post(`${baseURL}/api/upload/init-upload`, {
      filename: "stable-test-file.bin",
      filesize: totalSize,
      filehash: fileHash,
      totalChunks: testChunks,
    });
    
    const uploadId = initResponse.data.uploadId;
    console.log("✓ Upload initialized:", uploadId);

    // Test 4: Upload with conservative settings
    console.log("\n4. Starting conservative upload...");
    const startTime = Date.now();
    
    const results = await uploadChunksConservative(uploadId, testChunks, 2); // Only 2 concurrent
    
    const endTime = Date.now();
    const totalDuration = endTime - startTime;
    const totalMB = testChunks * 10;
    const overallSpeed = totalMB / (totalDuration / 1000);
    
    console.log(`\n📊 Final Statistics:`);
    console.log(`  - Total chunks: ${testChunks}`);
    console.log(`  - Total size: ${totalMB} MB`);
    console.log(`  - Total time: ${(totalDuration / 1000).toFixed(2)} seconds`);
    console.log(`  - Overall speed: ${overallSpeed.toFixed(2)} MB/s`);
    console.log(`  - Success rate: ${(results.length / testChunks * 100).toFixed(1)}%`);

    // Test 5: Check final status
    console.log("\n5. Checking final status...");
    const statusResponse = await axios.get(`${baseURL}/api/upload/upload-status`, {
      params: { uploadId },
    });
    console.log("✓ Final status:", {
      progress: statusResponse.data.progress,
      receivedChunks: statusResponse.data.receivedChunks.length,
      totalChunks: statusResponse.data.totalChunks,
      status: statusResponse.data.status
    });

    if (statusResponse.data.receivedChunks.length === testChunks) {
      console.log("\n6. Completing upload...");
      const completeResponse = await axios.post(`${baseURL}/api/upload/complete-upload`, {
        uploadId,
      });
      console.log("✓ Upload completed:", completeResponse.data.status);
    }

    console.log("\n✅ Stable upload test completed successfully!");

  } catch (error) {
    console.error("❌ Stable upload test failed:", {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      code: error.code,
    });
  }
}

// Run test
testStableUpload();
