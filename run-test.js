const { spawn } = require('child_process');
const axios = require('axios');

const baseURL = "http://localhost:3000";

async function checkServerRunning() {
  try {
    await axios.get(`${baseURL}/api/upload/test`);
    return true;
  } catch (error) {
    return false;
  }
}

async function runTest(testFile) {
  console.log(`🧪 Running test: ${testFile}`);
  
  // Check if server is running
  const isRunning = await checkServerRunning();
  if (!isRunning) {
    console.log("❌ Server is not running. Please start server first:");
    console.log("   npm start");
    console.log("   or");
    console.log("   node src/app.js");
    return;
  }
  
  console.log("✅ Server is running, starting test...\n");
  
  // Run test
  const testProcess = spawn('node', [testFile], {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  testProcess.on('close', (code) => {
    if (code === 0) {
      console.log(`\n✅ Test ${testFile} completed successfully`);
    } else {
      console.log(`\n❌ Test ${testFile} failed with code ${code}`);
    }
  });
  
  testProcess.on('error', (error) => {
    console.error(`❌ Failed to run test: ${error.message}`);
  });
}

// Get test file from command line arguments
const testFile = process.argv[2];

if (!testFile) {
  console.log("Usage: node run-test.js <test-file>");
  console.log("Examples:");
  console.log("  node run-test.js test-50mb.js");
  console.log("  node run-test.js test-5gb.js");
  console.log("  node run-test.js test-stable-upload.js");
  process.exit(1);
}

runTest(testFile);
