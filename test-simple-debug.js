const axios = require("axios");

const baseURL = "http://localhost:3000";

async function simpleTest() {
  try {
    console.log("🔍 Simple Debug Test...");
    
    // Test 1: Basic connectivity
    console.log("\n1. Testing basic connectivity...");
    const response = await axios.get(`${baseURL}/api/upload/test`);
    console.log("✅ Server response:", response.data);
    
    // Test 2: Check if we can reach the server
    console.log("\n2. Testing server health...");
    console.log("✅ Server is reachable and responding");
    
    // Test 3: Simple upload test
    console.log("\n3. Testing simple upload...");
    const testData = {
      filename: "debug-test.txt",
      filesize: 1024,
      filehash: "test-hash-123",
      totalChunks: 1
    };
    
    const initResponse = await axios.post(`${baseURL}/api/upload/init-upload`, testData);
    console.log("✅ Init upload successful:", initResponse.data);
    
    console.log("\n✅ All basic tests passed! Server is working correctly.");
    
  } catch (error) {
    console.error("❌ Test failed:");
    console.error("  Message:", error.message);
    console.error("  Code:", error.code);
    console.error("  Response:", error.response?.data);
    console.error("  Status:", error.response?.status);
    
    // Additional debugging info
    if (error.code === 'ECONNREFUSED') {
      console.error("\n🔧 Debug: Server is not running or not accessible");
      console.error("   - Make sure server is started with: node src/app.js");
      console.error("   - Check if port 3000 is available");
    } else if (error.code === 'ENOTFOUND') {
      console.error("\n🔧 Debug: DNS/Network issue");
      console.error("   - Check your network connection");
      console.error("   - Try using 127.0.0.1 instead of localhost");
    } else if (error.response?.status >= 500) {
      console.error("\n🔧 Debug: Server error");
      console.error("   - Check server logs for detailed error");
    }
  }
}

simpleTest();
